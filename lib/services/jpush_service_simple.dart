import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:jpush_flutter/jpush_flutter.dart';

/// 极光推送服务类 - 简化版，统一处理跳转逻辑
class JPushService {
  static final JPushService _instance = JPushService._internal();
  factory JPushService() => _instance;
  JPushService._internal();

  static const String _tag = 'JPushService';

  // 极光推送配置
  static const String _appKey = 'e4b1e2e32964eff17b239b67';

  JPush? _jpush;
  String? _registrationId;

  // 跳转回调 - 统一处理跳转逻辑
  Function(String type, Map<String, dynamic> data)? onNavigate;

  /// 初始化极光推送
  Future<void> initialize() async {
    try {
      _jpush = JPush();

      // 设置调试模式
      if (kDebugMode) {
        _jpush!.setDebugMode(true);
      }

      // 初始化
      _jpush!.setup(
        appKey: _appKey,
        channel: Platform.isIOS ? 'App Store' : 'developer-default',
        production: false,
      );

      // 获取 Registration ID
      _registrationId = await _jpush!.getRegistrationID();
      debugPrint('$_tag: Registration ID: $_registrationId');

      // 设置监听器
      _setupListeners();

      // 检查是否有待处理的推送通知
      await _checkPendingNotification();

      debugPrint('$_tag: 极光推送初始化成功');
    } catch (e) {
      debugPrint('$_tag: 极光推送初始化失败: $e');
    }
  }

  /// 设置监听器
  void _setupListeners() {
    if (_jpush == null) return;

    try {
      // 接收通知回调方法
      _jpush!.addEventHandler(
        onReceiveNotification: (Map<String, dynamic> message) async {
          debugPrint('$_tag: 接收到通知: $message');
        },

        // 点击通知回调方法 - 统一处理跳转
        onOpenNotification: (Map<String, dynamic> message) async {
          debugPrint('$_tag: 点击通知: $message');
          _handleNotificationClick(message);
        },

        // 接收自定义消息回调方法
        onReceiveMessage: (Map<String, dynamic> message) async {
          debugPrint('$_tag: 接收到自定义消息: $message');
        },

        // 接收Registration ID回调方法
        onReceiveRegistrationId: (Map<String, dynamic> message) async {
          _registrationId = message['registrationId'];
          debugPrint('$_tag: Registration ID 更新: $_registrationId');
        },
      );
    } catch (e) {
      debugPrint('$_tag: 设置监听器失败: $e');
    }
  }

  /// 检查是否有待处理的推送通知
  Future<void> _checkPendingNotification() async {
    try {
      if (Platform.isAndroid) {
        // Android: 从 Intent 中获取推送数据
        await _checkAndroidPendingNotification();
      } else if (Platform.isIOS) {
        // iOS: 从 UserDefaults 中获取推送数据
        await _checkIOSPendingNotification();
      }
    } catch (e) {
      debugPrint('$_tag: 检查待处理推送失败: $e');
    }
  }

  /// 检查 Android 待处理的推送
  Future<void> _checkAndroidPendingNotification() async {
    try {
      const platform = MethodChannel('com.jiyoujiaju.jijiahui/jpush');
      final String? notificationData =
          await platform.invokeMethod('getPendingNotification');

      if (notificationData != null && notificationData.isNotEmpty) {
        final Map<String, dynamic> data = json.decode(notificationData);
        debugPrint('$_tag: 处理 Android 待处理推送: $data');
        _handleNotificationClick(data);

        // 清除已处理的数据
        await platform.invokeMethod('clearPendingNotification');
      }
    } catch (e) {
      debugPrint('$_tag: 检查 Android 待处理推送失败: $e');
    }
  }

  /// 检查 iOS 待处理的推送
  Future<void> _checkIOSPendingNotification() async {
    try {
      const platform = MethodChannel('com.jiyoujiaju.jijiahui/jpush');
      final String? notificationData =
          await platform.invokeMethod('getPendingNotification');

      if (notificationData != null && notificationData.isNotEmpty) {
        final Map<String, dynamic> data = json.decode(notificationData);
        debugPrint('$_tag: 处理 iOS 待处理推送: $data');
        _handleNotificationClick(data);

        // 清除已处理的数据
        await platform.invokeMethod('clearPendingNotification');
      }
    } catch (e) {
      debugPrint('$_tag: 检查 iOS 待处理推送失败: $e');
    }
  }

  /// 统一处理通知点击事件 - 只在 Flutter 端处理跳转
  void _handleNotificationClick(Map<String, dynamic> message) {
    try {
      debugPrint('$_tag: 处理通知点击: $message');

      // 解析附加数据
      Map<String, dynamic>? extras;

      if (Platform.isAndroid) {
        // Android 端的附加数据可能在 extras 字段中
        if (message['extras'] != null) {
          if (message['extras'] is String) {
            extras = json.decode(message['extras']);
          } else {
            extras = Map<String, dynamic>.from(message['extras']);
          }
        } else {
          extras = message;
        }
      } else if (Platform.isIOS) {
        // iOS 端的附加数据直接在 message 中
        extras = message;
      }

      if (extras != null) {
        final type = extras['type'] as String?;

        debugPrint('$_tag: 推送类型: $type, 数据: $extras');

        // 调用跳转回调
        if (onNavigate != null && type != null) {
          onNavigate!(type, extras);
        } else {
          // 默认跳转处理
          _defaultNavigationHandler(type, extras);
        }
      }
    } catch (e) {
      debugPrint('$_tag: 处理通知点击失败: $e');
    }
  }

  /// 默认跳转处理
  void _defaultNavigationHandler(String? type, Map<String, dynamic> data) {
    switch (type) {
      case 'device':
        final deviceId = data['deviceId'] as String?;
        debugPrint('$_tag: 默认处理 - 跳转到设备详情页: $deviceId');
        // TODO: 在这里添加具体的设备页面跳转逻辑
        break;
      case 'home':
        debugPrint('$_tag: 默认处理 - 跳转到首页');
        // TODO: 在这里添加具体的首页跳转逻辑
        break;
      default:
        debugPrint('$_tag: 默认处理 - 未知推送类型: $type');
    }
  }

  /// 获取 Registration ID
  String? get registrationId => _registrationId;

  /// 设置别名
  Future<bool> setAlias(String alias) async {
    try {
      if (_jpush == null) return false;

      await _jpush!.setAlias(alias);
      debugPrint('$_tag: 设置别名成功: $alias');
      return true;
    } catch (e) {
      debugPrint('$_tag: 设置别名失败: $e');
      return false;
    }
  }

  /// 设置标签
  Future<bool> setTags(List<String> tags) async {
    try {
      if (_jpush == null) return false;

      await _jpush!.setTags(tags);
      debugPrint('$_tag: 设置标签成功: $tags');
      return true;
    } catch (e) {
      debugPrint('$_tag: 设置标签失败: $e');
      return false;
    }
  }

  /// 清除所有通知
  Future<void> clearAllNotifications() async {
    try {
      if (_jpush == null) return;

      await _jpush!.clearAllNotifications();
      debugPrint('$_tag: 清除所有通知');
    } catch (e) {
      debugPrint('$_tag: 清除所有通知失败: $e');
    }
  }
}
